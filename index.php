<?php
include './backend/config.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ชำระภาษีออนไลน์</title>
  <link rel="shortcut icon" href="./assets/image/icon/favicon.ico">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.8/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.1/css/all.min.css" rel="stylesheet">
  <style>
    body,
    html {
      height: 100%;
    }

    .center-container {
      min-height: 100vh;
    }
  </style>
</head>

<body>
  <div class="container center-container d-flex justify-content-center align-items-center">
    <div class="w-100" style="max-width: 500px;">
      <h2 class="text-center"><img src="./assets/image/icon/favicon.jpg" width="90px" height="90px" alt=""></h2>
      <h2 class="text-center">ชำระภาษีออนไลน์</h2>
      <div class="text-center" style="font-size:1rem;">องค์การบริหารส่วนตำบลเขาชนกัน</div><br>
      <form action="./backend/upload.php" method="post" enctype="multipart/form-data" class="needs-validation"
        novalidate>
        <!-- Name -->
        <div class="mb-3">
          <label for="name" class="form-label">ชื่อ-นามสกุล (กรุณากรอกชื่อและนามสกุลอย่างเป็นทางการ)</label>
          <input type="text" class="form-control" id="name" name="name" required>
          <div class="invalid-feedback">โปรดระบุชื่อและนามสกุลตามข้อมูลทางราชการ</div>
        </div>

        <!-- Phone -->
        <div class="mb-3">
          <label for="phone" class="form-label">หมายเลขโทรศัพท์</label>
          <input type="tel" class="form-control" id="phone" name="phone" pattern="[0-9]{10}" required>
          <div class="invalid-feedback">กรุณากรอกหมายเลขโทรศัพท์จำนวน 10 หลักตามข้อมูลทางราชการ</div>
        </div>
        <!-- Tax Type Selection -->
        <div class="mb-3">
          <label class="form-label">ประเภทภาษีที่ต้องการชำระ</label>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="taxType" id="taxType1" value="ภาษีโรงเรือนและที่ดิน"
              required>
            <label class="form-check-label" for="taxType1">
              ใบ ภ.ด.ส.๖
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="taxType" id="taxType2" value="ภาษีบำรุงท้องที่" required>
            <label class="form-check-label" for="taxType2">
              ใบ ภ.ป.๓
            </label>
          </div>
        </div>

        <!-- File Upload -->
        <div class="mb-3">
          <label for="file1" class="form-label">โปรดอัปโหลดสำเนาหนังสือแจ้งประเมิน (ภ.ด.ส.6):</label>
          <input type="file" class="form-control" id="file1" name="fileToUpload[]" accept="image/*" required
            onchange="previewImage(event, 'preview1')">
          <img id="preview1" src="" alt="ตัวอย่างภาพแรก" class="img-thumbnail mt-2"
            style="display:none; max-width: 200px; margin-left:auto; margin-right:auto; display:block; cursor:pointer;"
            onclick="zoomImage(this)">
        </div>

        <div class="mb-3 text-center">
          <label class="form-label">สแกนเพื่อชำระเงินผ่าน QR Code:</label><br>
          <img src="./assets/image/qr/qr-payment.jpg" alt="QR Code" class="img-thumbnail mt-2" style="max-width: 200px;"
            id="qrImage">
          <br>
          <a href="./assets/image/qr/qr-payment.jpg" download="qr-payment.jpg" class="btn btn-success mt-2">ดาวน์โหลด QR
            Code</a>
        </div>

        <div class="mb-3">
          <label for="file2" class="form-label">โปรดอัปโหลดหลักฐานการชำระเงิน (สลิปโอนเงิน):</label>
          <input type="file" class="form-control" id="file2" name="fileToUpload[]" accept="image/*" required
            onchange="previewImage(event, 'preview2')">
          <img id="preview2" src="" alt="ตัวอย่างสลิปโอนเงิน" class="img-thumbnail mt-2"
            style="display:none; max-width: 200px; margin-left:auto; margin-right:auto; display:block; cursor:pointer;"
            onclick="zoomImage(this)">
        </div>

        <script>
          // Add file size validation
          function validateFileSize(input) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (input.files[0].size > maxSize) {
              Swal.fire({
                icon: 'error',
                title: 'ไฟล์มีขนาดใหญ่เกินไป',
                text: 'กรุณาอัปโหลดไฟล์ขนาดไม่เกิน 5MB',
                confirmButtonText: 'ตกลง'
              });
              input.value = '';
              return false;
            }
            return true;
          }

          // Improved image preview function
          function previewImage(event, previewId) {
            const input = event.target;
            if (!validateFileSize(input)) return;
            const preview = document.getElementById(previewId);
            if (input.files && input.files[0]) {
              const reader = new FileReader();
              reader.onload = function (e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
              }
              reader.readAsDataURL(input.files[0]);
            } else {
              preview.src = '#';
              preview.style.display = 'none';
            }
          }

          function zoomImage(img) {
            const modalImg = document.getElementById('modalImage');
            modalImg.src = img.src;
            const zoomModal = new bootstrap.Modal(document.getElementById('imageZoomModal'));
            zoomModal.show();
          }
        </script>

        <!-- Submit -->
        <button type="submit" class="btn btn-primary w-100" name="submit">ส่งข้อมูล</button>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
          // Improved form submission
          document.querySelector('form').addEventListener('submit', async function (event) {
            event.preventDefault();

            if (!this.checkValidity()) {
              event.stopPropagation();
              this.classList.add('was-validated');
              return;
            }

            try {
              const result = await Swal.fire({
                icon: 'question',
                title: 'ยืนยันการส่งข้อมูล',
                text: 'คุณต้องการส่งข้อมูลใช่หรือไม่?',
                showCancelButton: true,
                confirmButtonText: 'ยืนยัน',
                cancelButtonText: 'ยกเลิก'
              });

              if (result.isConfirmed) {
                this.submit();
              }
            } catch (error) {
              Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาด',
                text: 'กรุณาลองใหม่อีกครั้ง',
                confirmButtonText: 'ตกลง'
              });
            }
          });
        </script>
      </form>
    </div>
  </div>

  <!-- Image Zoom Modal -->
  <div class="modal fade" id="imageZoomModal" tabindex="-1" aria-labelledby="imageZoomModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content bg-transparent border-0">
        <div class="modal-body p-0 d-flex justify-content-center align-items-center">
          <img id="modalImage" src="#" alt="Zoomed image"
            style="max-width: 100%; max-height: 80vh; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.3);">
        </div>
      </div>
    </div>
  </div>

  <?php include './backend/footer.php'; ?>
  <!-- Bootstrap JS + Validation Script -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.8/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Bootstrap custom validation
    (() => {
      'use strict';
      const forms = document.querySelectorAll('.needs-validation');
      Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
          if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
          }
          form.classList.add('was-validated');
        }, false);
      });
    })();
  </script>
</body>

</html>