<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tex_payment";

try {
  // First connect to create database if it doesn't exist
  $conn = new PDO("mysql:host=$servername", $username, $password);
  $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  // Create database if it doesn't exist
  $conn->exec("CREATE DATABASE IF NOT EXISTS $dbname");

  // Now connect to the specific database
  $conn = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
  $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

} catch(PDOException $e) {
  die("Connection failed: " . $e->getMessage());
}
?>