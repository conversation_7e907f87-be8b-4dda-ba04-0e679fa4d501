<?php
include 'config.php';

if (isset($_POST['submit'])) {
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $uploadDir = './assets/uploads/';

    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $files = $_FILES['fileToUpload'];
    $filePaths = array();

    for ($i = 0; $i < count($files['name']); $i++) {
        $fileName = time() . '_' . basename($files['name'][$i]);
        $targetFile = $uploadDir . $fileName;

        if (move_uploaded_file($files['tmp_name'][$i], $targetFile)) {
            $filePaths[] = 'uploads/' . $fileName;
        } else {
            die("Sorry, there was an error uploading your file.");
        }
    }

    $sql = "INSERT INTO member (name, phone, image1, image2) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);

    if ($stmt->execute([$name, $phone, $filePaths[0], $filePaths[1]])) {
        echo "<script>
            alert('บันทึกข้อมูลเรียบร้อยแล้ว');
            window.location.href = '../index.php';
        </script>";
    } else {
        echo "Error: " . $sql . "<br>" . $stmt->errorInfo()[2];
    }

    $stmt = null;
    $conn = null;
}
?>